# 移动文件至启动目录
mv /home/<USER>/bms_can_service /etc/init.d/bms_can_service

# 验证移动成功
ls -l /etc/init.d/bms_can_service

# 授权执行
chmod +x /home/<USER>/can_bms_uploader.sh
chmod +x /etc/init.d/bms_can_service

# 开机启动并运行
/etc/init.d/bms_can_service enable
/etc/init.d/bms_can_service start

# 验证收否创建成功
ls -l /etc/rc.d | grep 
/etc/init.d/bms_can_service status

# 一次性关闭（停止当前运行）
/etc/init.d/bms_uploader stop

# 永久禁用（开机不再启动）
/etc/init.d/bms_uploader disable

# 确认是否删除
ls /etc/rc.d | grep bms_uploader

/etc/init.d/bms_can_service status       # 查看状态（若支持）
logread | grep bms_can_service          # 查看日志（标准输出）
tail -f /home/<USER>/output.log # 查看文件日志
ps | grep python3                      # 查看是否在运行


chmod +x /home/<USER>/init_env.sh

logread | grep bms_can_service