# CAN接口配置
CAN_INTERFACE = "can0"
BITRATE = 250000

# 上传配置
UPLOAD_INTERVAL = 10  # 秒

TEST_UPLOAD_URL = "http://101.132.146.244:7001/bms-data/hexData"
PROD_UPLOAD_URL = "http://139.196.127.156:7001/bms-data/hexData"
UPLOAD_URL = PROD_UPLOAD_URL

ENABLE_LOCAL_LOG = True
LOCAL_LOG_PATH = "/home/<USER>/can_data_backup.json"

# 车辆信息
VIN = "AYH7414116"

# CAN接口强制初始化
FORCE_INIT_CAN = False

# 错误日志文件
ERROR_LOG = "/home/<USER>/uploader_error.log"

# CAN ID列表
CAN_ID_LIST = [
    # BMS相关ID
    218070019, 218070275, 218070531, 218070787, 218071043, 218071299, 218071555,
    
    #单体电压相关ID
    402709507, 402775043, 402840579, 402906115, 402971651, 403037187, 403102723,
    403168259, 403233795, 403299331, 403364867, 403430403, 403495939, 403561475,
    403627011, 403692547, 403758083, 403823619, 403889155, 403954691, 404020227,
    404085763, 404151299, 404216835, 404282371, 404347907, 404413443, 404478979,
    404544515, 404610051, 404675587, 404741123, 404806659, 404872195, 404937731,
    405003267, 405068803, 405134339, 405199875, 405265411, 405330947, 405396483,
    405462019, 405527555, 405593091, 405658627, 405724163, 405789699, 405855235,
    405920771, 405986307, 406051843,
    
    # 温感检测相关ID
    415292419, 415357955, 415423491, 415489027, 415554563, 415620099, 415685635,
    415751171, 415816707, 415882243, 415947779, 416013315, 416078851, 416220124,
    416340995, 416406531, 416537603, 416603139,
    
    # 其他ID
    550, 422
]

# 将列表转换为集合以提高查找效率
CAN_ID_SET = set(CAN_ID_LIST)

# 配置验证函数
def validate_config():
    """验证配置参数的有效性"""
    errors = []
    
    if not CAN_INTERFACE:
        errors.append("CAN_INTERFACE 不能为空")
    
    if BITRATE <= 0:
        errors.append("BITRATE 必须大于0")
    
    if UPLOAD_INTERVAL <= 0:
        errors.append("UPLOAD_INTERVAL 必须大于0")
    
    if not UPLOAD_URL:
        errors.append("UPLOAD_URL 不能为空")
    
    if not VIN:
        errors.append("VIN 不能为空")
    
    if not CAN_ID_LIST:
        errors.append("CAN_ID_LIST 不能为空")
    
    return errors

# 打印配置信息
def print_config():
    """打印当前配置信息"""
    print("=" * 50)
    print("CAN Logger 配置信息")
    print("=" * 50)
    print(f"CAN接口: {CAN_INTERFACE}")
    print(f"比特率: {BITRATE}")
    print(f"上传间隔: {UPLOAD_INTERVAL}秒")
    print(f"上传URL: {UPLOAD_URL}")
    print(f"本地日志: {'启用' if ENABLE_LOCAL_LOG else '禁用'}")
    print(f"日志路径: {LOCAL_LOG_PATH}")
    print(f"车辆VIN: {VIN}")
    print(f"强制初始化: {'是' if FORCE_INIT_CAN else '否'}")
    print(f"监听CAN ID数量: {len(CAN_ID_LIST)}")
    print("=" * 50)

if __name__ == "__main__":
    # 如果直接运行此文件，显示配置信息和验证结果
    print_config()
    
    errors = validate_config()
    if errors:
        print("\n配置错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n✓ 配置验证通过")
