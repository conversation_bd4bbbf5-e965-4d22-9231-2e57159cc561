#!/bin/sh

# CAN Logger 日志清理脚本
# SCRIPT_DIR="/home/<USER>"
# LOG_FILE="$SCRIPT_DIR/output.log"
# ERROR_LOG="$SCRIPT_DIR/uploader_error.log"
# DATA_BACKUP_FILE = "$SCRIPT_DIR/can_data_backup.json"
LOG_DIR="/home/<USER>"
LOG_FILES="can_data_backup.json uploader_error.log output.log"

MAX_SIZE=1048576  # 1MB

echo "[+] 清理日志中..."

# 清理数据日志文件
for file in $LOG_FILES; do
    FILE_PATH="$LOG_DIR/$file"
    if [ -f "$FILE_PATH" ]; then
        size=$(wc -c < "$FILE_PATH" 2>/dev/null)
        echo "${file}文件大小: ${size} 字节"
        if [ $size -gt $MAX_SIZE ]; then
            echo "{}" > "$FILE_PATH"
            echo "清空数据日志文件: $FILE_PATH (原大小: ${size} 字节)"
        else
            echo "数据日志文件大小正常，无需清理"
        fi
    else
        echo "数据日志文件不存在: $FILE_PATH"
    fi

done

echo "日志清理检查完成"
