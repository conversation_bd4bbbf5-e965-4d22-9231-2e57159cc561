{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1902f3af", "metadata": {}, "outputs": [], "source": ["import yaml\n", "import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 2, "id": "ff252f75", "metadata": {}, "outputs": [], "source": ["excel_file_path = \"/Users/<USER>/Desktop/协议文件/自研桩国标桩协议/G3-CAN_BMS2DEX.xlsx\"\n", "yaml_file_path = \"can_ids.yaml\""]}, {"cell_type": "code", "execution_count": 4, "id": "7bf50f90", "metadata": {}, "outputs": [], "source": ["df = pd.read_excel(excel_file_path)\n", "\n", "can_id_list = df['Dec'].dropna().astype(int).tolist()\n", "yaml_data = {'can_id_list': can_id_list}\n", "\n", "# # 写入 YAML 文件\n", "# with open(yaml_file_path, 'w') as file:\n", "#     yaml.dump(yaml_data, file, sort_keys=False, default_flow_style=False)"]}, {"cell_type": "code", "execution_count": 5, "id": "8ac39e3a", "metadata": {}, "outputs": [{"data": {"text/plain": ["[218070019,\n", " 218070275,\n", " 218070531,\n", " 218070787,\n", " 218071043,\n", " 218071299,\n", " 218071555,\n", " 402709507,\n", " 402775043,\n", " 402840579,\n", " 402906115,\n", " 402971651,\n", " 403037187,\n", " 403102723,\n", " 403168259,\n", " 403233795,\n", " 403299331,\n", " 403364867,\n", " 403430403,\n", " 403495939,\n", " 403561475,\n", " 403627011,\n", " 403692547,\n", " 403758083,\n", " 403823619,\n", " 403889155,\n", " 403954691,\n", " 404020227,\n", " 404085763,\n", " 404151299,\n", " 404216835,\n", " 404282371,\n", " 404347907,\n", " 404413443,\n", " 404478979,\n", " 404544515,\n", " 404610051,\n", " 404675587,\n", " 404741123,\n", " 404806659,\n", " 404872195,\n", " 404937731,\n", " 405003267,\n", " 405068803,\n", " 405134339,\n", " 405199875,\n", " 405265411,\n", " 405330947,\n", " 405396483,\n", " 405462019,\n", " 405527555,\n", " 405593091,\n", " 405658627,\n", " 405724163,\n", " 405789699,\n", " 405855235,\n", " 405920771,\n", " 405986307,\n", " 406051843,\n", " 415292419,\n", " 415357955,\n", " 415423491,\n", " 415489027,\n", " 415554563,\n", " 415620099,\n", " 415685635,\n", " 415751171,\n", " 415816707,\n", " 415882243,\n", " 415947779,\n", " 416013315,\n", " 416078851,\n", " 416220124]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["yaml_data['can_id_list'] # 73 values"]}, {"cell_type": "code", "execution_count": null, "id": "6d0b6971", "metadata": {}, "outputs": [], "source": ["bms_id_dict = [416220124, 404544515, 415947779, 416013315, 405789699, 405724163, 218070019, 403954691, 403889155,\n", "               403823619, 404478979, 404413443, 405658627, 405593091, 405527555, 405462019, 405396483,\n", "               405330947, 405265411, 405199875, 405134339, 405068803, 405003267, 404937731, 404872195, 404806659,\n", "               404741123, 404675587, 404610051, 403758083, 403692547, 403627011, 403561475, 403495939, 403430403,\n", "               403364867, 403299331, 403233795, 403168259, 403102723, 403037187, 402971651, 402906115, 402840579,\n", "               402775043, 402709507, 404347907, 404282371, 404216835, 404151299, 404085763, 404020227, 415882243,\n", "               218070275, 218071299, 218071043, 416537603, 416603139, 422, 550, 416209923, 416144387, 416078851,\n", "               218071555, 218070787, 218070531,\n", "               406051843,\n", "\t\t\t   415292419,\n", "\t\t\t   415357955,\n", "\t\t\t   415423491,\n", "\t\t\t   415489027,\n", "\t\t\t   415554563,\n", "\t\t\t   415620099,\n", "\t\t\t   415685635,\n", "\t\t\t   415751171,\n", "\t\t\t   415816707,\n", "\t\t\t   415882243,\n", "\t\t\t   25996291,\n", "\t\t\t   416013315,\n", "\t\t\t   26008579,\n", "\t\t\t   416340995,\n", "\t\t\t   416406531,\n", "\t\t\t   417061891,\n", "\t\t\t   218071811,\n", "               405855235,\n", "\t\t\t   405920771,\n", "\t\t\t   405986307\n", "] # 87 values, 85 in fact"]}, {"cell_type": "code", "execution_count": 9, "id": "527ab513", "metadata": {}, "outputs": [{"data": {"text/plain": ["list"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["type(bms_id_dict)"]}, {"cell_type": "code", "execution_count": 10, "id": "13fd5c36", "metadata": {}, "outputs": [], "source": ["def compare_lists(yaml_list, py_list):\n", "    only_in_yaml = set(yaml_list) - set(py_list)\n", "    only_in_py = set(py_list) - set(yaml_list)\n", "    return only_in_yaml, only_in_py"]}, {"cell_type": "code", "execution_count": 12, "id": "cc59dbb8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ YAML only: set()\n", "✅ Python only: {416209923, 218071811, 416340995, 416144387, 550, 416406531, 25996291, 422, 416537603, 417061891, 416603139, 26008579}\n"]}], "source": ["\n", "    only_in_yaml, only_in_py = compare_lists(yaml_data['can_id_list'], bms_id_dict)\n", "\n", "    print(f\"✅ YAML only: {only_in_yaml}\")\n", "    print(f\"✅ Python only: {only_in_py}\")"]}, {"cell_type": "code", "execution_count": 14, "id": "fc206957", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["len(only_in_py)"]}, {"cell_type": "code", "execution_count": null, "id": "929c0fa0", "metadata": {}, "outputs": [], "source": ["416340995：0x18D0DC03 #BMS发送总电压用于排查继电器故障\n", "416406531：0x18D1DC03 #BMS发送极限保护的脱钩请求指令和故障代码\n", "416537603：0x18D3DC03 #BMS发送 BMS 当前工作模式，与 VCU模式对应\n", "416603139：0x18D4DC03 #BMS补电数据协议\n", "550：0x226 #车辆底盘自动模式速度数据\n", "422：0x1A6 #车辆底盘故障数据\n", "\n", "\n", "416209923：0x18CEDC03\n", "218071811：0xCFF8303\n", "416144387：0x18CDDC03\n", "25996291：0x18CAC03\n", "417061891：0x18DBDC03\n", "26008579：0x18CDC03\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}