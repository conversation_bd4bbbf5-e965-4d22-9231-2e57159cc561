# BMS CAN Logger 系统

一个集成的 BMS CAN 数据采集、上传和管理系统，适用于 OpenWrt 环境。

## 系统概述

本系统集成了三个核心功能：
- **bms_uploader**: CAN 数据采集和上传
- **bms_can_service**: OpenWrt procd 服务管理
- **clean_log**: 自动日志清理

## 文件结构

```
G3/
├── deploy.sh              # 一键部署脚本
├── bms_Uploader.py        # 主程序 - CAN数据采集和上传
├── config.py              # 配置文件
├── clean_log.sh           # 日志清理脚本
├── bms_can_service        # OpenWrt服务文件模板
└── README.md              # 本文档
```

## 核心文件功能

### 1. bms_Uploader.py
- **功能**: CAN 数据采集和 HTTP 上传
- **特性**: 
  - 自动配置 CAN 接口
  - 实时数据采集和缓存
  - 定时上传到服务器
  - 错误限频和日志记录

### 2. config.py
- **功能**: 系统配置参数
- **包含**: CAN接口、上传URL、车辆VIN等配置

### 3. deploy.sh
- **功能**: 一键部署脚本
- **特性**:
  - 自动创建目录和文件
  - 生成 OpenWrt 服务文件
  - 设置定时清理任务
  - 启动服务并验证

### 4. clean_log.sh
- **功能**: 日志文件清理
- **特性**: 当日志文件超过1MB时自动清空

## 快速开始

### 1. 部署系统

```bash
# 1. 设置执行权限
chmod +x deploy.sh

# 2. 运行部署脚本
./deploy.sh
```

### 2. 部署过程

部署脚本会自动执行以下步骤：

1. ✅ 创建工作目录 `/home/<USER>
2. ✅ 创建必要的日志文件
3. ✅ 生成 OpenWrt 服务文件 `/etc/init.d/bms_can_service`
4. ✅ 创建日志清理脚本
5. ✅ 设置定时清理任务（每6小时）
6. ✅ 启用并启动服务
7. ✅ 验证服务状态

## 验证运行状态

### 1. 检查服务状态
```bash
# 查看服务状态
/etc/init.d/bms_can_service status

# 检查是否设置开机自启动
ls /etc/rc.d | grep bms_can_service
```

### 2. 查看运行进程
```bash
# 查看 Python 进程
ps | grep bms_Uploader.py

# 查看所有相关进程
ps | grep -E "(bms|python3)"
```

### 3. 监控日志
```bash
# 实时查看运行日志
tail -f /home/<USER>/output.log

# 查看错误日志
tail -f /home/<USER>/uploader_error.log

# 查看系统日志
logread | grep bms_can_service
```

### 4. 检查定时任务
```bash
# 查看 crontab 任务
crontab -l | grep clean_log
```

## 服务管理

### 基本操作
```bash
# 启动服务
/etc/init.d/bms_can_service start

# 停止服务
/etc/init.d/bms_can_service stop

# 重启服务
/etc/init.d/bms_can_service restart

# 启用开机自启动
/etc/init.d/bms_can_service enable

# 禁用开机自启动
/etc/init.d/bms_can_service disable
```

### 手动清理日志
```bash
# 执行日志清理
/home/<USER>/clean_log.sh
```

## 配置说明

### config.py 主要参数

```python
# CAN接口配置
CAN_INTERFACE = "can0"          # CAN接口名称
BITRATE = 250000                # CAN波特率

# 上传配置
UPLOAD_INTERVAL = 10            # 上传间隔(秒)
UPLOAD_URL = "http://..."       # 上传服务器地址

# 车辆信息
VIN = "AYH7413105"             # 车辆识别码

# 日志配置
ERROR_LOG = "/home/<USER>/uploader_error.log"
LOCAL_LOG_PATH = "/home/<USER>/can_data_backup.json"
```

## 目录结构

部署后的系统目录结构：

```
/home/<USER>/
├── bms_Uploader.py           # 主程序
├── config.py                 # 配置文件
├── clean_log.sh              # 清理脚本
├── output.log                # 运行日志
├── uploader_error.log        # 错误日志
├── bms_Uploader.pid          # 进程ID文件
└── can_data_backup.json      # 数据备份文件

/etc/init.d/
└── bms_can_service           # OpenWrt 服务文件
```

## 故障排除

### 1. 服务无法启动
```bash
# 检查文件是否存在
ls -la /home/<USER>/bms_Uploader.py
ls -la /home/<USER>/config.py

# 检查权限
ls -la /etc/init.d/bms_can_service

# 手动测试 Python 脚本
cd /home/<USER>
python3 bms_Uploader.py
```

### 2. CAN 接口问题
```bash
# 检查 CAN 接口状态
ifconfig can0

# 手动配置 CAN 接口
ip link set can0 type can bitrate 250000
ifconfig can0 up
```

### 3. 日志文件过大
```bash
# 手动清理日志
/home/<USER>/clean_log.sh

# 检查磁盘空间
df -h
```

## 系统特性

### 1. 自动重启
- ✅ 机器重启后自动启动服务
- ✅ 进程崩溃后自动重新启动
- ✅ procd 进程监控和管理

### 2. 日志管理
- ✅ 自动日志轮转（超过1MB清空）
- ✅ 定时清理任务（每6小时）
- ✅ 错误日志带时间戳

### 3. 错误处理
- ✅ CAN 接口自动配置
- ✅ 网络错误重试机制
- ✅ 错误限频避免日志爆炸

## 技术支持

如遇问题，请提供以下信息：
1. 服务状态：`/etc/init.d/bms_can_service status`
2. 运行日志：`tail -50 /home/<USER>/output.log`
3. 错误日志：`tail -50 /home/<USER>/uploader_error.log`
4. 系统日志：`logread | grep bms_can_service`
